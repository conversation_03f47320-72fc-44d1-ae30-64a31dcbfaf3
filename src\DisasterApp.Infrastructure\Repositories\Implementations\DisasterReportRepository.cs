﻿using DisasterApp.Domain.Entities;
using DisasterApp.Infrastructure.Data;
using DisasterApp.Infrastructure.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Infrastructure.Repositories
{
    public class DisasterReportRepository : IDisasterReportRepository
    {
        private readonly DisasterDbContext _context;
        public  DisasterReportRepository(DisasterDbContext context)
        {
            _context = context; 
        }
        public async Task<IEnumerable<DisasterReport>> GetAllAsync()
        {
            return await _context.DisasterReports
                .Include(r => r.DisasterEvent)
                .Include(r => r.User)
                //.Include(r => r.Location)
                //.Include(r => r.Photos)
                .Include(r => r.ImpactDetails)
                .ToListAsync();
        }
        public async Task<DisasterReport?> GetByIdAsync(Guid id)
        {
            return await _context.DisasterReports
                .Include(r => r.DisasterEvent)
                .Include(r => r.User)
                //.Include(r => r.Location)
                //.Include(r => r.Photos)
                .Include(r => r.ImpactDetails)
                .FirstOrDefaultAsync(r => r.Id == id);
        }
        public async Task<DisasterReport> AddAsync(DisasterReport report)
        {
            _context.DisasterReports.Add(report);
            await _context.SaveChangesAsync();
            return report;
        }

        public async Task DeleteAsync(Guid id)
        {
            var report = _context.DisasterReports.Find(id);
            if (report != null)
            {
                _context.DisasterReports.Remove(report);
                await _context.SaveChangesAsync();
            }
        }
             

        public async Task UpdateAsync(DisasterReport report)
        {
            _context.DisasterReports.Update(report);
            await _context.SaveChangesAsync();
        }
    }
}
