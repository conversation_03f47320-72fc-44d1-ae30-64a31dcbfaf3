﻿using DisasterApp.Application.DTOs;
using DisasterApp.Domain.Entities;
using DisasterApp.Domain.Enums;
using DisasterApp.Infrastructure.Data;
using DisasterApp.Infrastructure.Repositories;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.Services
{
    public class DisasterReportService : IDisasterReportService
    {
        private readonly IDisasterReportRepository _repository;
        private readonly DisasterDbContext _context;
        public DisasterReportService(IDisasterReportRepository repository,DisasterDbContext context)
        {
            _repository = repository;
            _context = context;
        }
        public async Task<DisasterReport> CreateAsync(DisasterReportCreateDto dto, Guid userId)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                // 1. Handle DisasterEvent
                Guid disasterEventId = dto.DisasterEventId ?? Guid.Empty;
                if (disasterEventId == Guid.Empty && !string.IsNullOrWhiteSpace(dto.NewDisasterEventName))
                {
                    var newEvent = new DisasterEvent
                    {
                        Id = Guid.NewGuid(),
                        Name = dto.NewDisasterEventName,
                        DisasterTypeId = dto.DisasterTypeId
                    };
                    _context.DisasterEvents.Add(newEvent);
                    //await _context.SaveChangesAsync();
                    disasterEventId = newEvent.Id;
                }

                // 2. Create Report
                var report = new DisasterReport
                {
                    Id = Guid.NewGuid(),
                    Title = dto.Title,
                    Description = dto.Description,
                    Timestamp = dto.Timestamp,
                    Severity = dto.Severity,
                    Status = ReportStatus.Pending,
                    UserId = userId,
                    DisasterEventId = disasterEventId,
                    CreatedAt = DateTime.UtcNow,
                    //Location = new Location
                    //{
                    //    LocationId = Guid.NewGuid(),
                    //    Latitude = dto.Latitude,
                    //    Longitude = dto.Longitude
                    //}
                };

                // 3. Handle ImpactDetails (with null check)
                if (dto.ImpactDetails != null)
                {
                    foreach (var impactDto in dto.ImpactDetails)
                    {
                        int impactTypeId = impactDto.ImpactTypeId.GetValueOrDefault();
                        if (impactTypeId == 0 && !string.IsNullOrWhiteSpace(impactDto.ImpactTypeName))
                        {
                            var newImpactType = new ImpactType
                            {
                                Name = impactDto.ImpactTypeName
                            };
                            _context.ImpactTypes.Add(newImpactType);
                           // await _context.SaveChangesAsync();
                            impactTypeId = newImpactType.Id;
                        }

                        report.ImpactDetails.Add(new ImpactDetail
                        {
                            ReportId = report.Id,
                            ImpactTypeId = impactTypeId,
                            Description = impactDto.Description,
                            Severity = impactDto.Severity
                        });
                    }
                }

                // 4. Save Report
                await _repository.AddAsync(report);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return report;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                Console.WriteLine($"[ERROR] CreateAsync failed: {ex}");
                throw;
            }

        }



        public async Task<IEnumerable<DisasterReportDto>> GetAllAsync()
        {
            var reports=await _repository.GetAllAsync();

            var activeReports = reports.Where(r => r.IsDeleted != true);

            var result = activeReports.Select(r => new DisasterReportDto
            {
                Id = r.Id,
                Title = r.Title,
                Description = r.Description,
                Timestamp = r.Timestamp,
                Severity = r.Severity,
                Status = r.Status,
                DisasterEventName = r.DisasterEvent?.Name,
                UserName = r.User.Name,

                ImpactDetails = r.ImpactDetails.Select(i => new ImpactDetailDto
                {
                    ImpactTypeName = i.ImpactType?.Name ?? string.Empty,
                    Description = i.Description,
                    Severity = i.Severity,
                    IsResolved = i.IsResolved
                }).ToList()
            }).ToList();
            return result;
        }

        public async Task<DisasterReportDto?> GetByIdAsync(Guid id)
        {
            var report = await _repository.GetByIdAsync(id);
            if (report ==null || report.IsDeleted==true) return null;

            var dto = new DisasterReportDto
            {
                Id = report.Id,
                Title = report.Title,
                Description = report.Description,
                Timestamp = report.Timestamp,
                Severity = report.Severity,
                Status = report.Status,
                DisasterEventName = report.DisasterEvent?.Name,
                UserName = report.User.Name,
                Latitude = report.Location?.Latitude,
                Longitude = report.Location?.Longitude,
                ImpactDetails = report.ImpactDetails.Select(i => new ImpactDetailDto
                {
                    ImpactTypeName = i.ImpactType?.Name ?? string.Empty,
                    Description = i.Description,
                    Severity = i.Severity,
                    IsResolved = i.IsResolved
                }).ToList()
            };
            return dto;
        }

        public async Task<DisasterReport?> UpdateAsync(Guid id, DisasterReportCreateDto dto)
        {
            var report = await _context.DisasterReports
                .Include(r => r.Location)
                .Include(r => r.ImpactDetails)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (report == null) return null;

            // Update basic info
            report.Title = dto.Title;
            report.Description = dto.Description;
            report.Timestamp = dto.Timestamp;
            report.Severity = dto.Severity;

            // Update DisasterEvent (optional)
            if (dto.DisasterEventId.HasValue)
            {
                report.DisasterEventId = dto.DisasterEventId.Value;
            }
            else if (!string.IsNullOrWhiteSpace(dto.NewDisasterEventName))
            {
                var newEvent = new DisasterEvent
                {
                    Id = Guid.NewGuid(),
                    Name = dto.NewDisasterEventName,
                    DisasterTypeId = dto.DisasterTypeId
                };
                _context.DisasterEvents.Add(newEvent);
                await _context.SaveChangesAsync();
                report.DisasterEventId = newEvent.Id;
            }

            // Update location
            //if (report.Location != null)
            //{
            //    report.Location.Latitude = dto.Latitude;
            //    report.Location.Longitude = dto.Longitude;
            //}

            // Update impacts
            _context.ImpactDetails.RemoveRange(report.ImpactDetails); // Clear old
            report.ImpactDetails.Clear();   
           

            foreach (var impactDto in dto.ImpactDetails)
            {
                int impactTypeId = impactDto.ImpactTypeId.GetValueOrDefault();
                if (impactTypeId == 0 && !string.IsNullOrWhiteSpace(impactDto.ImpactTypeName))
                {
                    var newImpactType = new ImpactType
                    {
                        Name = impactDto.ImpactTypeName
                    };
                    _context.ImpactTypes.Add(newImpactType);
                    await _context.SaveChangesAsync();
                    impactTypeId = newImpactType.Id;
                }

                report.ImpactDetails.Add(new ImpactDetail
                {
                    ReportId = report.Id,
                    Description = impactDto.Description,
                    Severity = impactDto.Severity,
                   // IsResolved = impactDto.IsResolved,
                    ImpactTypeId = impactTypeId
                });
            }

            await _context.SaveChangesAsync();
            return report;
        }

        public async Task<bool> DeleteAsync(Guid id)
        {
            var report = await _context.DisasterReports
                .Include(r => r.ImpactDetails)
                .Include(r => r.Location)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (report == null) return false;

            _context.ImpactDetails.RemoveRange(report.ImpactDetails);
            if (report.Location != null)
                _context.Locations.Remove(report.Location);

            _context.DisasterReports.Remove(report);
            await _context.SaveChangesAsync();

            return true;
        }

    }
}
