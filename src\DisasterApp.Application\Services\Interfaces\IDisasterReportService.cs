﻿using DisasterApp.Application.DTOs;
using DisasterApp.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.Services
{
    public interface IDisasterReportService
    {
        Task<DisasterReport> CreateAsync(DisasterReportCreateDto dot, Guid userId);
        Task<IEnumerable<DisasterReportDto>> GetAllAsync();
        Task<DisasterReportDto?> GetByIdAsync(Guid id);
        Task<DisasterReport?> UpdateAsync(Guid id, DisasterReportCreateDto dto);
        Task<bool> DeleteAsync(Guid id);
    }
}
