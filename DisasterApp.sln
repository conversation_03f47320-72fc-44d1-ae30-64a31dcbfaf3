Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{FA709CDC-1633-6FFB-58EB-C86CDB72BC5E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DisasterApp", "src\DisasterApp.WebApi\DisasterApp.csproj", "{204A31DF-AA04-43F6-83AB-B830DF2AD120}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DisasterApp.Application", "src\DisasterApp.Application\DisasterApp.Application.csproj", "{C6F565DC-F2AC-42DE-A817-B0C691B52CE9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DisasterApp.Infrastructure", "src\DisasterApp.Infrastructure\DisasterApp.Infrastructure.csproj", "{C843BB24-254A-4CEA-A78A-AA2269F1AA6B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DisasterApp.Domain", "src\DisasterApp.Domain\DisasterApp.Domain.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DisasterApp.Tests", "src\DisasterApp.Tests\DisasterApp.Tests.csproj", "{37D6A77F-F0AB-420E-9493-D4B693D5DF5D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{204A31DF-AA04-43F6-83AB-B830DF2AD120}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{204A31DF-AA04-43F6-83AB-B830DF2AD120}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{204A31DF-AA04-43F6-83AB-B830DF2AD120}.Debug|x64.ActiveCfg = Debug|Any CPU
		{204A31DF-AA04-43F6-83AB-B830DF2AD120}.Debug|x64.Build.0 = Debug|Any CPU
		{204A31DF-AA04-43F6-83AB-B830DF2AD120}.Debug|x86.ActiveCfg = Debug|Any CPU
		{204A31DF-AA04-43F6-83AB-B830DF2AD120}.Debug|x86.Build.0 = Debug|Any CPU
		{204A31DF-AA04-43F6-83AB-B830DF2AD120}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{204A31DF-AA04-43F6-83AB-B830DF2AD120}.Release|Any CPU.Build.0 = Release|Any CPU
		{204A31DF-AA04-43F6-83AB-B830DF2AD120}.Release|x64.ActiveCfg = Release|Any CPU
		{204A31DF-AA04-43F6-83AB-B830DF2AD120}.Release|x64.Build.0 = Release|Any CPU
		{204A31DF-AA04-43F6-83AB-B830DF2AD120}.Release|x86.ActiveCfg = Release|Any CPU
		{204A31DF-AA04-43F6-83AB-B830DF2AD120}.Release|x86.Build.0 = Release|Any CPU
		{C6F565DC-F2AC-42DE-A817-B0C691B52CE9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C6F565DC-F2AC-42DE-A817-B0C691B52CE9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C6F565DC-F2AC-42DE-A817-B0C691B52CE9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C6F565DC-F2AC-42DE-A817-B0C691B52CE9}.Debug|x64.Build.0 = Debug|Any CPU
		{C6F565DC-F2AC-42DE-A817-B0C691B52CE9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C6F565DC-F2AC-42DE-A817-B0C691B52CE9}.Debug|x86.Build.0 = Debug|Any CPU
		{C6F565DC-F2AC-42DE-A817-B0C691B52CE9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C6F565DC-F2AC-42DE-A817-B0C691B52CE9}.Release|Any CPU.Build.0 = Release|Any CPU
		{C6F565DC-F2AC-42DE-A817-B0C691B52CE9}.Release|x64.ActiveCfg = Release|Any CPU
		{C6F565DC-F2AC-42DE-A817-B0C691B52CE9}.Release|x64.Build.0 = Release|Any CPU
		{C6F565DC-F2AC-42DE-A817-B0C691B52CE9}.Release|x86.ActiveCfg = Release|Any CPU
		{C6F565DC-F2AC-42DE-A817-B0C691B52CE9}.Release|x86.Build.0 = Release|Any CPU
		{C843BB24-254A-4CEA-A78A-AA2269F1AA6B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C843BB24-254A-4CEA-A78A-AA2269F1AA6B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C843BB24-254A-4CEA-A78A-AA2269F1AA6B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C843BB24-254A-4CEA-A78A-AA2269F1AA6B}.Debug|x64.Build.0 = Debug|Any CPU
		{C843BB24-254A-4CEA-A78A-AA2269F1AA6B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C843BB24-254A-4CEA-A78A-AA2269F1AA6B}.Debug|x86.Build.0 = Debug|Any CPU
		{C843BB24-254A-4CEA-A78A-AA2269F1AA6B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C843BB24-254A-4CEA-A78A-AA2269F1AA6B}.Release|Any CPU.Build.0 = Release|Any CPU
		{C843BB24-254A-4CEA-A78A-AA2269F1AA6B}.Release|x64.ActiveCfg = Release|Any CPU
		{C843BB24-254A-4CEA-A78A-AA2269F1AA6B}.Release|x64.Build.0 = Release|Any CPU
		{C843BB24-254A-4CEA-A78A-AA2269F1AA6B}.Release|x86.ActiveCfg = Release|Any CPU
		{C843BB24-254A-4CEA-A78A-AA2269F1AA6B}.Release|x86.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x86.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x86.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x86.Build.0 = Release|Any CPU
		{37D6A77F-F0AB-420E-9493-D4B693D5DF5D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{37D6A77F-F0AB-420E-9493-D4B693D5DF5D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{37D6A77F-F0AB-420E-9493-D4B693D5DF5D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{37D6A77F-F0AB-420E-9493-D4B693D5DF5D}.Debug|x64.Build.0 = Debug|Any CPU
		{37D6A77F-F0AB-420E-9493-D4B693D5DF5D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{37D6A77F-F0AB-420E-9493-D4B693D5DF5D}.Debug|x86.Build.0 = Debug|Any CPU
		{37D6A77F-F0AB-420E-9493-D4B693D5DF5D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{37D6A77F-F0AB-420E-9493-D4B693D5DF5D}.Release|Any CPU.Build.0 = Release|Any CPU
		{37D6A77F-F0AB-420E-9493-D4B693D5DF5D}.Release|x64.ActiveCfg = Release|Any CPU
		{37D6A77F-F0AB-420E-9493-D4B693D5DF5D}.Release|x64.Build.0 = Release|Any CPU
		{37D6A77F-F0AB-420E-9493-D4B693D5DF5D}.Release|x86.ActiveCfg = Release|Any CPU
		{37D6A77F-F0AB-420E-9493-D4B693D5DF5D}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{204A31DF-AA04-43F6-83AB-B830DF2AD120} = {FA709CDC-1633-6FFB-58EB-C86CDB72BC5E}
		{C6F565DC-F2AC-42DE-A817-B0C691B52CE9} = {FA709CDC-1633-6FFB-58EB-C86CDB72BC5E}
		{C843BB24-254A-4CEA-A78A-AA2269F1AA6B} = {FA709CDC-1633-6FFB-58EB-C86CDB72BC5E}
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890} = {FA709CDC-1633-6FFB-58EB-C86CDB72BC5E}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {59C97F0E-BDE2-4E0F-B8CE-54526E6EE6CE}
	EndGlobalSection
EndGlobal
