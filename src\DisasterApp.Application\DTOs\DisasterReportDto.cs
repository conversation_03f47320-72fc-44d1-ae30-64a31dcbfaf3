﻿using DisasterApp.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.DTOs
{
    public class DisasterReportCreateDto
    {
        public string Title { get; set; } = null!;
        public string Description { get; set; } = null!;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public SeverityLevel Severity { get; set; }
        public ReportStatus Status { get; set; } = ReportStatus.Pending;

        public int DisasterTypeId { get; set; }
        public Guid? DisasterEventId { get; set; }
        public string? NewDisasterEventName { get; set; }

        //public decimal Latitude { get; set; }
        //public decimal Longitude { get; set; }

        public List<ImpactDetailCreateDto> ImpactDetails { get; set; } = new List<ImpactDetailCreateDto>();

        //public List<IFormFile>? Photos { get; set; }
    }

    public class DisasterReportDto
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = null!;
        public string Description { get; set; } = null!;
        public DateTime Timestamp { get; set; }
        public SeverityLevel Severity { get; set; }
        public ReportStatus Status { get; set; }
        public string DisasterEventName { get; set; } = null!;
        public string UserName { get; set; } = null!;
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public List<ImpactDetailDto> ImpactDetails { get; set; } = new List<ImpactDetailDto>();
    }

}