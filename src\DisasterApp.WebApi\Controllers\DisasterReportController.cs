﻿using DisasterApp.Application.DTOs;
using DisasterApp.Application.Services;
using Microsoft.AspNetCore.Mvc;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace DisasterApp.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DisasterReportController : ControllerBase
    {
        private readonly IDisasterReportService _service;
        public DisasterReportController(IDisasterReportService service)
        {
            _service = service;
        }
        // GET: api/<DisasterReportController>
        [HttpGet]
        public async Task<IActionResult> GetAll() => Ok(await _service.GetAllAsync());


        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            var report = await _service.GetByIdAsync(id);
            if (report == null) return NotFound();
            return Ok(report);
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] DisasterReportCreateDto dto)
        {
            var userId = Guid.NewGuid(); // Replace with real UserId from auth context
            var report = await _service.CreateAsync(dto, userId);
            return CreatedAtAction(nameof(GetById), new { id = report.Id }, report);
        }

        // PUT api/<DisasterReportController>/5
        [HttpPut("{id}")]
        public void Put(int id, [FromBody] string value)
        {
        }

        // DELETE api/<DisasterReportController>/5
        [HttpDelete("{id}")]
        public void Delete(int id)
        {
        }
    }
}
