{"ConnectionStrings": {"DefaultConnection": "Server=DESKTOP-OQOPD5E;Database=Disaster;User ID=sa;Password=*****;"}, "Jwt": {"Key": "ThisIsAVeryLongSecretKeyForJWTTokenGenerationThatShouldBeAtLeast32Characters", "Issuer": "DisasterApp", "Audience": "DisasterAppUsers", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpiratidonDays": 30}, "GoogleAuth": {"ClientId": "153648153927-208kh1b7vt3tfgid4eec9fsqmln9p2ie.apps.googleusercontent.com", "ClientSecret": "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"}, "PasswordReset": {"ExpirationHours": 1}, "Frontend": {"BaseUrl": "http://localhost:5173"}, "Email": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SenderEmail": "<EMAIL>", "SenderName": "DisasterWatch Team", "Username": "<EMAIL>", "Password": "baey liea wmkr usfx", "EnableSsl": true}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}