﻿using DisasterApp.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Infrastructure.Repositories
{
    public interface IDisasterReportRepository
    {
        Task<DisasterReport> AddAsync(DisasterReport report);
        Task<DisasterReport?> GetByIdAsync(Guid id);
        Task<IEnumerable<DisasterReport>> GetAllAsync();
        Task UpdateAsync(DisasterReport report);
        Task DeleteAsync(Guid id);
    }
}
