﻿using DisasterApp.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DisasterApp.Application.DTOs
{
    public class ImpactDetailCreateDto
    {
        public int? ImpactTypeId { get; set; }
        public string? ImpactTypeName { get; set; }
        public string Description { get; set; } = null!;
        public SeverityLevel Severity { get; set; }
        
    }
    public class ImpactDetailDto
    {
        public string Description { get; set; } = null!;
        public SeverityLevel? Severity { get; set; }
        public bool? IsResolved { get; set; }
        public DateTime? ResolvedAt { get; set; }
        public string ImpactTypeName { get; set; } = null!;
    }
}